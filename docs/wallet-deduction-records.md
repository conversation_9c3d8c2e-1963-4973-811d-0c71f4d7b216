# 钱包补差记录方案实现文档

## 概述

钱包补差记录方案用于记录用户使用钱包余额支付时产生的补差需求，并自动向艺术家转账补差金额。

## 核心功能

### 1. 数据库设计

#### 钱包补差记录表 (wallet_deduction_records)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| user_id | bigint | 用户ID |
| wallet_transaction_id | int | 关联的钱包交易ID |
| order_id | int | 关联的订单ID |
| artist_user_id | bigint | 艺术家用户ID |
| artist_withdraw_account_id | int | 艺术家Stripe提现账户ID |
| amount | int | 补差金额(单位:分) |
| currency_id | int | 货币ID |
| status | enum | 补差状态 |
| stripe_transfer_id | string | Stripe转账ID |
| compensated_at | timestamp | 补差完成时间 |
| failure_reason | text | 失败原因 |
| remark | text | 备注 |
| metadata | json | 额外元数据 |

#### 补差状态枚举 (WalletDeductionStatus)

- `pending`: 待处理
- `processing`: 处理中  
- `completed`: 已完成
- `failed`: 失败
- `cancelled`: 已取消

### 2. 核心组件

#### 模型 (WalletDeductionRecord)

- 位置: `app/Models/WalletDeductionRecord.php`
- 功能: 数据模型，包含关联关系和作用域查询
- 关联关系:
  - `user()`: 用户
  - `artistUser()`: 艺术家用户
  - `walletTransaction()`: 钱包交易
  - `order()`: 订单
  - `currency()`: 货币
  - `artistWithdrawAccount()`: 艺术家提现账户

#### 枚举 (WalletDeductionStatus)

- 位置: `app/Enums/WalletDeductionStatus.php`
- 功能: 定义补差记录的状态值

#### Stripe转账服务 (StripeTransferService)

- 位置: `app/Service/Stripe/StripeTransferService.php`
- 功能: 
  - 执行单个补差转账
  - 批量处理待补差记录
  - 错误处理和日志记录

#### 内部管理控制器 (InternalWalletDeductionController)

- 位置: `app/Http/Controllers/Api/Internal/InternalWalletDeductionController.php`
- 功能:
  - 查询补差记录列表
  - 手动执行补差
  - 批量处理补差
  - 统计信息查询

#### 定时任务命令 (ProcessWalletDeductions)

- 位置: `app/Console/Commands/ProcessWalletDeductions.php`
- 功能: 定时处理待补差记录
- 调度: 每5分钟执行一次

### 3. 业务流程

#### 补差记录创建流程

1. 用户使用钱包余额支付订单
2. `WorkTaskPayController::processWalletDeductions()` 处理钱包扣减
3. 创建钱包交易记录
4. 检查艺术家是否有有效的Stripe Recipient账户
5. 创建补差记录，状态为 `pending`

#### 补差执行流程

1. 定时任务或手动触发补差处理
2. `StripeTransferService::transferToArtist()` 执行转账
3. 更新记录状态为 `processing`
4. 调用Stripe API创建转账
5. 转账成功: 更新状态为 `completed`，记录转账ID和完成时间
6. 转账失败: 更新状态为 `failed`，记录失败原因

### 4. API接口

#### 内部管理API

基础路径: `/api/internal/wallet-deduction`

| 方法 | 路径 | 功能 | 参数 |
|------|------|------|------|
| GET | `/records` | 获取补差记录列表 | status, user_id, artist_user_id, start_date, end_date, page, per_page |
| POST | `/manual-compensate/{id}` | 手动执行补差 | id |
| POST | `/batch-process` | 批量处理补差 | limit |
| GET | `/statistics` | 获取统计信息 | start_date, end_date |

#### 认证方式

使用Bearer Token认证，token配置在 `config('auth.internal_api_token')`

### 5. 定时任务

#### 配置

在 `routes/console.php` 中配置:

```php
Schedule::command(ProcessWalletDeductions::class, ['--limit=20'])
    ->everyFiveMinutes()
    ->withoutOverlapping()
    ->runInBackground();
```

#### 手动执行

```bash
php artisan wallet:process-deductions --limit=50
```

### 6. 测试

#### 单元测试

- `tests/Feature/WalletDeductionRecordTest.php`: 模型和基础功能测试
- `tests/Feature/InternalWalletDeductionApiTest.php`: API接口测试

#### 运行测试

```bash
php artisan test tests/Feature/WalletDeductionRecordTest.php
php artisan test tests/Feature/InternalWalletDeductionApiTest.php
```

### 7. 监控和日志

#### 日志记录

- 转账成功: `Log::info('Stripe transfer completed')`
- 转账失败: `Log::error('Stripe transfer failed')`
- 艺术家账户无效: `Log::warning('Artist does not have valid Stripe recipient account')`

#### 监控指标

- 待处理记录数量
- 处理成功率
- 平均处理时间
- 失败原因分析

### 8. 部署说明

#### 数据库迁移

```bash
php artisan migrate
```

#### 环境配置

确保以下配置正确:
- Stripe API密钥
- 内部API认证token
- 定时任务调度器运行

#### 注意事项

1. 确保艺术家有有效的Stripe Recipient账户
2. 监控Stripe API调用限制
3. 定期检查失败记录并处理
4. 备份重要数据

## 总结

钱包补差记录方案已完整实现，包括数据库设计、业务逻辑、API接口、定时任务和测试。系统能够自动记录补差需求并执行转账，同时提供完善的管理和监控功能。
