<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('artist_applications', function (Blueprint $table) {
            $table->integer('id', true);
            $table->enum('status', ['pending', 'accepted', 'rejected'])->nullable();
            $table->integer('user_id')->nullable();
            $table->string('artist_name')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('artist_applications');
    }
};
