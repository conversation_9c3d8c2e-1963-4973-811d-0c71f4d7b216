<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_rec_reviews', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('user_id')->nullable();
            $table->integer('artist_id')->nullable();
            $table->integer('service_id')->nullable();
            $table->integer('work_task_id')->nullable();
            $table->integer('project_id')->nullable();
            $table->enum('type', ['service', 'project'])->nullable();
            $table->decimal('rating_score', 5, 1)->nullable();
            $table->json('rating_content')->nullable();
            $table->integer('is_deleted')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_rec_reviews');
    }
};
