<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_verify_codes', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('email');
            $table->string('code', 10);
            $table->string('token')->nullable();
            $table->enum('type', ['sign_up', 'forget_psw', 'change_email', 'manage_withdraw_account']);
            $table->enum('status', ['sent', 'verified', 'used']);
            $table->dateTime('expired_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_verify_codes');
    }
};
