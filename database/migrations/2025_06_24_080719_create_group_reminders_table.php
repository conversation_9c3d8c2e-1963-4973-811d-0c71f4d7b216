<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('group_reminders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('group_id')->index();
            $table->unsignedBigInteger('sender_id')->index();
            $table->unsignedBigInteger('template_id')->index();
            $table->json('mentioned_user_ids')->comment('被提醒的用户ID列表');
            $table->timestamp('remind_at')->comment('提醒时间');
            $table->enum('status', ['pending', 'successed', 'failed'])->default('pending');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['sender_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('group_reminders');
    }
};
