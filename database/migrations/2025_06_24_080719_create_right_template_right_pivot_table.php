<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('right_template_right_pivot', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('right_template_id')->nullable();
            $table->integer('right_id')->nullable();
            $table->integer('state')->nullable();
            $table->timestamps();

            $table->unique(['right_template_id', 'right_id'], 'idx_right_template_right');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('right_template_right_pivot');
    }
};
