<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currency_rates', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('base_currency_id')->nullable();
            $table->string('base_code')->nullable();
            $table->string('target_currency_id')->nullable();
            $table->string('target_code')->nullable();
            $table->tinyText('target_symbol')->nullable();
            $table->double('rate')->nullable();
            $table->timestamps();

            $table->unique(['base_code', 'target_code'], 'base_code_target_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currency_rates');
    }
};
