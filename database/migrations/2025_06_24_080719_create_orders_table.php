<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('user_id');
            $table->integer('artist_id')->nullable();
            $table->string('busable_type');
            $table->integer('busable_id');
            $table->enum('status', ['pending', 'paying', 'paid', 'cancelled'])->default('pending');
            $table->json('busable_info');
            $table->integer('amount');
            $table->integer('currency_id');
            $table->integer('init_amount');
            $table->integer('init_currency_id')->nullable();
            $table->json('amount_calc_process');
            $table->json('payment_info')->nullable();
            $table->json('stripe_fee')->nullable();
            $table->json('fee')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
