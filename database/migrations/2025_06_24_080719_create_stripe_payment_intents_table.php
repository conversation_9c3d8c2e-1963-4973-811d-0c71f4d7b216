<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stripe_payment_intents', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('payment_intent');
            $table->enum('pay_type', ['full_pay', 'stage_pay']);
            $table->integer('work_task_id');
            $table->integer('work_task_stage_id');
            $table->integer('service_request_response_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stripe_payment_intents');
    }
};
