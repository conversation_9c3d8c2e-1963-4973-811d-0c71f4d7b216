<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('upload_files', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->default(0);
            $table->string('busable_type')->nullable()->default('');
            $table->unsignedInteger('busable_id')->nullable()->default(0);
            $table->enum('state', ['pending', 'successed', 'deleted']);
            $table->string('scene');
            $table->string('name');
            $table->string('mime')->nullable();
            $table->integer('size')->nullable();
            $table->string('url_og');
            $table->timestamps();
            $table->softDeletes()->index('idx_upload_videos_deleted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('upload_files');
    }
};
