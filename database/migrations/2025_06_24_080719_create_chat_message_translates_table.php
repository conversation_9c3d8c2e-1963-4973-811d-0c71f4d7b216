<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_message_translates', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('chat_message_id');
            $table->unsignedInteger('chat_id');
            $table->unsignedInteger('translate_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_message_translates');
    }
};
