<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_request_responses', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('user_id');
            $table->integer('artist_id');
            $table->integer('service_id');
            $table->integer('service_request_id')->unique('service_request_id');
            $table->enum('status', ['received', 'accepted', 'rejected'])->nullable();
            $table->integer('currency_id')->nullable();
            $table->integer('accept_price')->nullable();
            $table->integer('accept_days_need')->nullable();
            $table->timestamp('accept_start_time')->nullable();
            $table->json('accept_content')->nullable();
            $table->enum('accept_plat_fee_type', ['buyer', 'seller'])->nullable();
            $table->enum('reject_type', ['style_not_suitable'])->nullable();
            $table->json('reject_content')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_request_responses');
    }
};
