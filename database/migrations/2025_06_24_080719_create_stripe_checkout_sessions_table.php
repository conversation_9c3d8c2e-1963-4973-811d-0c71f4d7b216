<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stripe_checkout_sessions', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('checkout_session_id');
            $table->enum('status', ['pending', 'processing', 'finished'])->default('pending');
            $table->integer('order_id');
            $table->string('client_secret', 1500);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stripe_checkout_sessions');
    }
};
