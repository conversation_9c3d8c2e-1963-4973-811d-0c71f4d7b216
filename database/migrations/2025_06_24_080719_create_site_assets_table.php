<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('site_assets', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('artwork_id')->nullable();
            $table->string('site')->nullable()->index('site');
            $table->integer('is_active')->nullable()->default(0)->index('is_active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('site_assets');
    }
};
