<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_user_pivot', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('chat_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('last_read_cursor')->nullable();
            $table->enum('chat_visable_status', ['can_see', 'can_not_see'])->nullable()->default('can_not_see')->index('chat_visable_status');
            $table->integer('send_message_count')->nullable()->default(0);
            $table->bigInteger('order_at_ts')->nullable()->default(0);
            $table->json('settings')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_user_pivot');
    }
};
