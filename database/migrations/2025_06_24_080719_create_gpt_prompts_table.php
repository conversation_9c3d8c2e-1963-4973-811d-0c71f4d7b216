<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gpt_prompts', function (Blueprint $table) {
            $table->increments('id');
            $table->string('type')->nullable();
            $table->string('action')->nullable();
            $table->string('table_column')->nullable();
            $table->longText('prompt')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gpt_prompts');
    }
};
