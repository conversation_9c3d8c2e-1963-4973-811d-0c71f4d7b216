<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_withdraw_accounts', function (Blueprint $table) {
            $table->enum('stripe_service_agreement', ['full', 'recipient'])->nullable()->after('stripe_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_withdraw_accounts', function (Blueprint $table) {
            $table->dropColumn('stripe_service_agreement');
        });
    }
};
