<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_requests', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('user_id');
            $table->integer('artist_id');
            $table->integer('service_id');
            $table->enum('status', ['pending', 'artist_accepted', 'artist_rejected', 'user_canceled'])->nullable()->default('pending');
            $table->string('reject_type')->nullable();
            $table->json('reject_content')->nullable();
            $table->integer('currency_id')->nullable();
            $table->timestamp('deadline')->nullable();
            $table->double('budget')->nullable();
            $table->json('name')->nullable();
            $table->json('detail')->nullable();
            $table->integer('artist_is_read')->default(0);
            $table->integer('user_is_read')->default(0);
            $table->integer('accept_price')->nullable();
            $table->integer('accept_days_need')->nullable();
            $table->timestamp('accept_start_time')->nullable();
            $table->json('accept_content')->nullable();
            $table->enum('accept_plat_fee_type', ['buyer', 'seller'])->nullable();
            $table->integer('accept_currency_id')->nullable();
            $table->integer('is_user_deleted')->nullable()->default(0);
            $table->integer('is_artist_deleted')->nullable()->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_requests');
    }
};
