<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_task_price_changes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('work_task_id');
            $table->unsignedBigInteger('currency_id');
            $table->string('initiator_type')->nullable();
            $table->unsignedBigInteger('initiator_id')->nullable();
            $table->string('approver_type')->nullable();
            $table->unsignedBigInteger('approver_id')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->integer('old_price');
            $table->integer('new_price');
            $table->integer('need_pay_amount');
            $table->integer('work_task_paid_amount');
            $table->enum('status', ['pending', 'wait_pay', 'paid', 'rejected', 'canceled']);
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();

            $table->index(['approver_type', 'approver_id']);
            $table->index(['initiator_type', 'initiator_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_task_price_changes');
    }
};
