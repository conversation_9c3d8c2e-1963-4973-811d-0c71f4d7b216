<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('artists', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->nullable()->unique('user_id');
            $table->integer('status')->nullable();
            $table->string('uid')->nullable();
            $table->string('name')->nullable();
            $table->integer('avatar_id')->nullable()->default(2);
            $table->integer('cover_id')->nullable();
            $table->json('intro')->nullable();
            $table->json('contract')->nullable();
            $table->integer('followers')->default(0);
            $table->integer('recommend_score')->nullable()->index('recommend_score');
            $table->float('worktask_plat_fee_ratio')->default(0.05);
            $table->float('product_plat_fee_ratio')->default(0);
            $table->float('pay_fee_ratio')->default(0.045);
            $table->string('link_youtube')->nullable();
            $table->string('link_pixiv')->nullable();
            $table->string('link_bilibili')->nullable();
            $table->string('link_twitch')->nullable();
            $table->string('link_facebook')->nullable();
            $table->string('link_twitter')->nullable();
            $table->string('link_instagram')->nullable();
            $table->string('link_weibo')->nullable();
            $table->string('link_personal')->nullable();
            $table->integer('is_open')->default(1);
            $table->float('weight')->default(1);
            $table->timestamps();
            $table->softDeletes();
            $table->integer('sort')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('artists');
    }
};
