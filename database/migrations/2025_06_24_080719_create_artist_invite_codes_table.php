<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('artist_invite_codes', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('inviter_user_id')->nullable();
            $table->integer('inviter_artist_id')->nullable();
            $table->integer('invitee_user_id')->nullable();
            $table->integer('invitee_artist_id')->nullable();
            $table->string('code', 10)->nullable()->unique('code');
            $table->enum('status', ['can_use', 'used', 'wait_confirm'])->nullable();
            $table->timestamp('used_at')->nullable();
            $table->integer('invite_code_type_id')->nullable()->index('invite_code_type_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('artist_invite_codes');
    }
};
