<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_showcases', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('artist_id');
            $table->integer('service_id')->nullable();
            $table->enum('type', ['artwork', 'upload_image', 'youtube_link', 'bilibili_link', 'upload_video'])->nullable();
            $table->integer('artwork_id')->nullable();
            $table->integer('upload_image_id')->nullable();
            $table->integer('upload_video_id')->nullable();
            $table->string('video_url')->nullable();
            $table->string('video_preview_image')->nullable();
            $table->integer('sort')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_showcases');
    }
};
