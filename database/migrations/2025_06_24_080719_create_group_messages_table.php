<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('group_messages', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('group_id');
            $table->integer('sender_uid')->nullable();
            $table->enum('content_type', ['text', 'system_notifiction'])->nullable();
            $table->json('content')->nullable();
            $table->bigInteger('ts')->nullable();
            $table->bigInteger('seq_id')->nullable()->default(0);
            $table->integer('translate_id')->nullable();
            $table->integer('is_undo')->nullable()->default(0);
            $table->integer('reply_message_id')->nullable();
            $table->string('c_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('group_messages');
    }
};
