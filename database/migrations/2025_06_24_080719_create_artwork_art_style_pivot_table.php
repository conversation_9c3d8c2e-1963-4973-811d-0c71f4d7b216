<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('artwork_art_style_pivot', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('artwork_id')->nullable();
            $table->integer('art_style_id')->nullable();

            $table->index(['artwork_id', 'art_style_id'], 'idx_artwork_id_art_style_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('artwork_art_style_pivot');
    }
};
