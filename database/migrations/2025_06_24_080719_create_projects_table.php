<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id');
            $table->integer('currency_id');
            $table->integer('status')->nullable();
            $table->json('name')->nullable();
            $table->integer('price_start')->nullable();
            $table->integer('price_end')->nullable();
            $table->timestamp('deadline')->nullable();
            $table->json('content')->nullable();
            $table->string('use_range')->nullable();
            $table->integer('right_template_id')->nullable();
            $table->integer('feedback_intervals')->nullable();
            $table->string('size_spec')->nullable();
            $table->integer('is_private')->default(0);
            $table->integer('is_archived')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
