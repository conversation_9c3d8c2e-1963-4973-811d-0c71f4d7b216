<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('artist_lang_pivot', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('artist_id')->nullable();
            $table->integer('language_id')->nullable();
            $table->tinyInteger('is_main')->nullable();
            $table->tinyInteger('fluent')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('artist_lang_pivot');
    }
};
