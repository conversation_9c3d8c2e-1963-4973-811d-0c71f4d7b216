<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallet_transactions', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->nullable();
            $table->integer('wallet_id')->nullable();
            $table->integer('currency_id')->nullable();
            $table->enum('action', ['plus', 'minus'])->nullable();
            $table->enum('biz_type', ['work_task', 'product', 'withdraw', 'gift', 'refund', 'invite_gift', 'plat_fee_deduct'])->nullable();
            $table->enum('status', ['pending', 'successed', 'failed'])->nullable();
            $table->integer('amount')->nullable();
            $table->integer('before_balance')->nullable();
            $table->integer('after_balance')->nullable();
            $table->string('note', 2048)->nullable();
            $table->json('detail')->nullable();
            $table->integer('withdraw_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_transactions');
    }
};
