<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_applicants', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('user_id');
            $table->enum('status', ['pending', 'accepted', 'rejected', 'canceled']);
            $table->json('reject_content')->nullable();
            $table->string('social_id');
            $table->string('social_link', 2000);
            $table->integer('artist_invite_code_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_applicants');
    }
};
