<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('translates', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id');
            $table->string('busable_type')->nullable();
            $table->string('busable_table')->nullable();
            $table->unsignedInteger('busable_id')->nullable();
            $table->string('busable_field')->nullable();
            $table->string('key')->nullable()->index('key');
            $table->string('key_decode')->nullable();
            $table->enum('status', ['pending', 'successed', 'cancelled']);
            $table->enum('translate_type', ['gpt', 'human']);
            $table->unsignedInteger('human_translate_user_id')->nullable();
            $table->string('source_lang');
            $table->json('source')->nullable();
            $table->integer('batteries')->nullable()->default(0);
            $table->integer('total_token')->nullable()->default(0);
            $table->string('target_lang')->nullable();
            $table->text('target_text')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('translates');
    }
};
