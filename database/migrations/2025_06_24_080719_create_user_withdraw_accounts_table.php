<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_withdraw_accounts', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('currency_id')->nullable();
            $table->enum('type', ['alipay', 'stripe']);
            $table->enum('bind_status', ['pending', 'successed'])->nullable();
            $table->string('alipay_account')->nullable();
            $table->string('alipay_name')->nullable();
            $table->string('stripe_connect_account_id')->nullable();
            $table->string('stripe_email')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_withdraw_accounts');
    }
};
