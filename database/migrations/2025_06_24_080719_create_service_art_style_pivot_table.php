<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_art_style_pivot', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('service_id');
            $table->integer('art_style_id');

            $table->index(['service_id', 'art_style_id'], 'idx_service_art_style');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_art_style_pivot');
    }
};
