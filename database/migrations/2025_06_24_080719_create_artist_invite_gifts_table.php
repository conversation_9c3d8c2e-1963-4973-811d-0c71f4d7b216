<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('artist_invite_gifts', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('name');
            $table->integer('invite_code_type_id');
            $table->enum('type', ['invitee_credit', 'inviter_credit', 'invitee_deposit', 'inviter_deposit', 'invitee_invite_code', 'invitee_plat_fee_deduct', 'inviter_plat_fee_deduct'])->nullable();
            $table->json('config')->nullable();
            $table->integer('currency_id')->nullable();
            $table->integer('amount')->nullable()->default(0);
            $table->json('content')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('artist_invite_gifts');
    }
};
