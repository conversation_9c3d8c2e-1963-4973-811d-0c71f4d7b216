<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('artworks', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('artist_id')->nullable();
            $table->tinyInteger('type')->nullable();
            $table->integer('is_show')->nullable()->default(0);
            $table->json('title')->nullable();
            $table->json('detail')->nullable();
            $table->enum('upload_type', ['upload_image', 'youtube_link', 'bilibili_link', 'upload_video'])->nullable();
            $table->integer('upload_image_id')->nullable();
            $table->integer('upload_video_id')->nullable();
            $table->string('video_url')->nullable();
            $table->string('video_preview_image')->nullable();
            $table->integer('sort')->nullable();
            $table->integer('likes')->nullable()->default(0);
            $table->integer('recommend_score')->nullable()->index('recommend_score');
            $table->integer('is_used_asset')->default(0);
            $table->integer('is_nsfw')->nullable()->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('artworks');
    }
};
