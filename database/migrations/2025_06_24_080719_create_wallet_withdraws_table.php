<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallet_withdraws', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id');
            $table->integer('wallet_id');
            $table->json('wallet_snapshot')->nullable();
            $table->integer('withdraw_account_id');
            $table->json('withdraw_account_snapshot')->nullable();
            $table->integer('amount');
            $table->enum('status', ['pending', 'accepted', 'rejected', 'cancelled']);
            $table->string('note', 2048)->default('');
            $table->string('reject_reason', 2048)->default('');
            $table->string('transfer_type', 2048)->default('');
            $table->string('transfer_id', 2048)->default('');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_withdraws');
    }
};
