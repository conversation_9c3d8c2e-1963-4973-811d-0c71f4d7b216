<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_orders', function (Blueprint $table) {
            $table->increments('id');
            $table->string('order_number', 191)->unique('order_number');
            $table->integer('user_id')->nullable();
            $table->enum('status', ['pending', 'paid'])->nullable();
            $table->integer('amount')->nullable();
            $table->integer('point_discount')->nullable();
            $table->integer('point_used')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_orders');
    }
};
