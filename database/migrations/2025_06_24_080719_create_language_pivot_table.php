<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('language_pivot', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('language_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('artist_id')->nullable();
            $table->enum('fluency', ['native', 'fluent', 'Intermediate'])->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('language_pivot');
    }
};
