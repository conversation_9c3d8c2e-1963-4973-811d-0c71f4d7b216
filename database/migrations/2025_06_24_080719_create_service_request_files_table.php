<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_request_files', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('service_request_id');
            $table->integer('user_id')->nullable();
            $table->integer('service_id')->nullable();
            $table->integer('artist_id')->nullable();
            $table->enum('type', ['upload_image', 'file'])->nullable();
            $table->integer('upload_image_id')->nullable();
            $table->json('file_name')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_request_files');
    }
};
