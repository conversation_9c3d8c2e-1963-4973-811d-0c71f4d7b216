<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_examples', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('project_id')->nullable();
            $table->enum('type', ['upload_image', 'youtube_link', 'bilibili_link'])->nullable();
            $table->integer('upload_image_id')->nullable();
            $table->longText('video_url')->nullable();
            $table->integer('sort')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_examples');
    }
};
